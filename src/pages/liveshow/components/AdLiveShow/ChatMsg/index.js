import './index.less';
import cls from 'classnames';
import React, {useEffect, useRef, useState} from 'react';
import Chat, {MessageType} from '@/pages/liveshow/utils/chat';
import emoticon from '@/pages/liveshow/utils/emoticon';
import {tinyScrollTo} from '@/utils/animation';
import {useAdLiveShowContext} from '../context';
import {enhanceImAdd, useEnhanceImShow} from '../hooks/useEnhanceImMonitor';
import {useEverInViewport} from '../hooks/useEverInViewport';

// 初始化加载im消息 1初始化 2im区域正在滚动 0初始化动画完成
let isInitScroll = 1;
// 是否展示悬浮im消息提示
let isShowLastMsg = false;

const showName = (name, hideName) => {
    if (!name) {
        return name;
    }

    if (hideName) {
        return `${name.slice(0, 1)}***`;
    }
    return name;
};

function UserName({username, q2c}) {
    return !!username && <span className={q2c
        ? 'q2c-user-name user-name' : 'user-name'}
    >{username}：{q2c ? '@我' : null}</span>;
}
function ChatItem({item, nid, status}) {
    let {username, content, q2c} = item;

    let props = {
        dangerouslySetInnerHTML: {
            __html: emoticon(content)
        }
    };
    const isContentIm = q2c === 'contentIm';

    const [ref, inViewport] = useEverInViewport();

    const {chatImDataResult = {}} = useAdLiveShowContext();
    const {
        openFlag,
        failCode
    } = chatImDataResult;

    useEnhanceImShow({
        inViewport, failCode,
        status, nid, screen,
        openFlag
    });

    return (
        <div className={cls('chat-item-container', {
            'q2c-chat-item-container': q2c,
            'q2c-back-bg': isContentIm
        })}
        >
            {q2c ? <div className='chat-item'>
                {isContentIm ? <>
                    <span className='author' ref={ref}>
                        主播
                    </span>
                    <UserName username={username} q2c={q2c} />
                </> : <UserName username={username} />}
                <span className='msg' {...props} />
            </div> : <div className='chat-item'>
                <UserName username={username} />
                <span className='msg' {...props} />
            </div>}
        </div>
    );
}

function ChatMsg(props) {
    const {
        nid, status, msgHlsUrl, hideName, chat_mcast_id,
        isLogin, isCNY, likeAction, chatList, setChatList, host
    } = props;
    const authorName = host.name ?? '';
    const $msgBox = useRef();
    const $msgWrapper = useRef();
    const $chatList = useRef();
    const chat = useRef(Chat.getInstance());
    const [lastMsg, setLastMsg] = useState('');
    const {chatImDataResult = {}} = useAdLiveShowContext();

    const {
        openFlag, // false-代表关闭，true-代表打开 关闭则屏蔽样式不展示（query和contentim皆不展示）
        query, // 为空或length=0，则评论区不展示用户query，仅展示contentIm
        contentIm, // 为空或length=0，则屏蔽样式不展示
        failCode
    } = chatImDataResult;

    const openQ2C = openFlag && !!contentIm;
    useEffect(() => {
        isInitScroll = 1;
        isShowLastMsg = false;

        initChat(openQ2C, query, contentIm);
    }, [query, openQ2C, contentIm]);

    useEffect(() => {
        if (!chatList.length) {
            return;
        }

        // 当前展示位置非im底部 悬浮提示最新消息
        if (isShowLastMsg) {
            let newMsg = chatList[chatList.length - 1];
            if (newMsg && newMsg.content) {
                setLastMsg(newMsg.content);
            }
            return;
        }

        // 正在执行初始化滚动 禁止其他操作
        if (isInitScroll === 2) {
            return;
        }

        // 当前展示位置是im底部 直接滚动展示新消息

        const lastMessage = chatList[chatList.length - 1];
        const isQ2C = lastMessage && isQ2CMessage(lastMessage);
        const wrapper = document.querySelector('.inner-wrapper');
        const userNameDom = wrapper?.querySelector('.q2c-user-name');

        scrollToBottom(isInitScroll ? 'shake' : '', isQ2C, wrapper, userNameDom);

        // 仅第一次加载im消息时执行
        if (isInitScroll) {
            isInitScroll = 2;
            initAddEvent();
        }
    }, [chatList]);

    const isQ2CMessage = msg => !!msg.q2c;
    const createTime1 = Math.floor(Date.now() / 1000);
    const q2cMessageItem = [
        {
            msgId: `q2c-content-${Date.now()}`,
            name: authorName,
            word: contentIm,
            avatarSrc: '',
            createTime: createTime1,
            responseUser: null,
            q2c: 'contentIm',
            content: {type: '0'},
            type: 0
        }];
    if (query) {
        q2cMessageItem.unshift({
            msgId: `q2c-query-${Date.now()}`,
            name: '我',
            word: query,
            avatarSrc: '',
            createTime: createTime1 - 1,
            responseUser: null,
            q2c: 'query',
            content: {type: '0'},
            type: 0
        });
    }
    const initChat = openQ2C => {
        chat.current.start({
            m3u8Link: msgHlsUrl,
            status: status,
            addDefaultMsg: true,
            q2cMessageList: openQ2C ? q2cMessageItem : [],
            chatID: chat_mcast_id,
            isLogin
        });

        chat.current.on(MessageType.MESSAGE, list => {
            enhanceImAdd({nid, screen, failCode, status});
            const newL = list.map(item => {
                return {
                    msgId: item.msgId,
                    avatarSrc: item.portrait,
                    username: showName(item.name, item?.q2c ? false : hideName),
                    publishTime: item.timestr,
                    content: item.word,
                    responseUser: item.responseUser,
                    q2c: item.q2c
                };
            });
            setChatList(newL);
        });
    };
    useEffect(() => {
        if (likeAction) {
            const newLikeMessage = {
                msgId: `like-${Date.now()}`,
                username: '',
                content: '你点赞了直播间',
                avatarSrc: '',
                publishTime: new Date().toISOString(),
                responseUser: null
            };
            setChatList(prev => [...prev, newLikeMessage]);
        }
    }, [likeAction]);

    const scrollToBottom = (type, isQ2C = false, wrapper, userNameDom) => {
        const element = $msgBox.current;
        isShowLastMsg = false;

        setLastMsg('');
        if (element) {
            const {scrollHeight, clientHeight} = element;
            const scrollDistance = scrollHeight - clientHeight;

            if (type === 'shake') {
                // 初始化滚动
                if (wrapper && userNameDom) {
                    const wrapperRect = wrapper.getBoundingClientRect();
                    const targetRect = userNameDom.getBoundingClientRect();

                    // 计算元素相对于容器顶部的偏移
                    const offsetTop = targetRect.top - wrapperRect.top;

                    // 目标滚动位置，距离顶部100px
                    const scrollTop = wrapper.scrollTop + offsetTop - 100;
                    wrapper.scrollTo({
                        top: scrollTop,
                        behavior: 'smooth'
                    });
                }
                else {
                    const duration = Math.min(2000, 100 * Math.max(4, chatList.length));
                    tinyScrollTo(element, 'scrollTop', scrollDistance, duration);

                    setTimeout(() => {
                        if ($chatList.current) {
                            $chatList.current.style.marginTop = 0;
                        }
                        isInitScroll = 0;
                    }, duration + 50);
                    return;
                }
            }

            // 非初始化滚动效果
            tinyScrollTo(element, 'scrollTop', scrollDistance, 200);
        }
    };

    const initAddEvent = () => {
        const element = $msgWrapper.current;
        if (element) {
            let startY = -1;
            let endY = -1;

            element.addEventListener('touchstart', e => {
                e.stopPropagation();

                if (e.touches[0]) {
                    startY = e.touches[0].screenY;
                }
            }, {passive: false});

            element.addEventListener('touchmove', e => {
                e.stopPropagation();

                if (e.touches[0]) {
                    endY = e.touches[0].screenY;
                }

                const isPull = startY < endY;
                const {scrollHeight, clientHeight, scrollTop} = $msgBox.current;
                const scrollDistance = scrollHeight - clientHeight;

                if (isPull && scrollTop === 0
                    || !isPull && scrollTop === scrollDistance) {
                    e.preventDefault();
                }
            }, {passive: false});

            element.addEventListener('touchend', e => {
                e.stopPropagation();

                const {scrollHeight, clientHeight, scrollTop} = $msgBox.current;
                if (scrollTop + 10 >= scrollHeight - clientHeight) {
                    isShowLastMsg = false;
                    setLastMsg('');
                }
                else {
                    isShowLastMsg = true;
                }
            }, {passive: false});
        }
    };

    return (
        <div
            className={cls('ad-chat-msg-wrapper', {
                'ad-chat-msg-wrapper-cny': isCNY
            })}
            ref={$msgWrapper}
        >
            <div
                className='inner-wrapper'
                ref={$msgBox}
            >
                <div
                    className="chat-list"
                    ref={$chatList}
                >
                    {
                        chatList.map((item, idx) => (
                            <ChatItem key={item.msgId || idx} item={item} nid={nid} status={status} />
                        ))
                    }
                </div>
            </div>

            {/* 新消息提示 点击滚动到im底部 */}
            {lastMsg && (
                <div
                    className='last-msg'
                    onClick={() => scrollToBottom('', false)}
                >
                    <div className='arrow' />
                    <div className='msg-text'>{lastMsg}</div>
                </div>
            )}
        </div>
    );
}

export default ChatMsg;
